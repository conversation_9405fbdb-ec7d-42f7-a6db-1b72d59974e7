"""
Audio Player Module
Handles audio playback functionality using pygame
"""
import pygame
import threading
import time
from pathlib import Path
from typing import Optional, Callable
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioPlayer:
    """Audio player class for handling audio playback"""
    
    def __init__(self):
        """Initialize the audio player"""
        self.is_initialized = False
        self.current_file = None
        self.is_playing_flag = False
        self.is_paused_flag = False
        self.position = 0
        self.duration = 0
        self.volume = 0.7
        
        # Callbacks
        self.position_callback = None
        self.finished_callback = None
        
        # Initialize pygame mixer
        self.init_pygame()
    
    def init_pygame(self):
        """Initialize pygame mixer"""
        try:
            pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=1024)
            pygame.mixer.init()
            self.is_initialized = True
            logger.info("Audio player initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize audio player: {e}")
            self.is_initialized = False
    
    def set_callbacks(
        self,
        position_callback: Callable[[float, float], None] = None,
        finished_callback: Callable[[], None] = None
    ):
        """
        Set callback functions for audio events
        
        Args:
            position_callback: Called with (current_position, duration) in seconds
            finished_callback: Called when playback finishes
        """
        self.position_callback = position_callback
        self.finished_callback = finished_callback
    
    def load(self, file_path: str) -> bool:
        """
        Load an audio file
        
        Args:
            file_path: Path to audio file
            
        Returns:
            True if loaded successfully, False otherwise
        """
        if not self.is_initialized:
            logger.error("Audio player not initialized")
            return False
        
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"Audio file not found: {file_path}")
                return False
            
            # Stop current playback if any
            self.stop()
            
            # Load the audio file
            pygame.mixer.music.load(str(file_path))
            self.current_file = str(file_path)
            
            # Try to get duration (this is approximate for some formats)
            self.duration = self._get_audio_duration(file_path)
            
            logger.info(f"Loaded audio file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load audio file: {e}")
            return False
    
    def _get_audio_duration(self, file_path: Path) -> float:
        """
        Get audio duration (approximate)
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Duration in seconds (0 if cannot determine)
        """
        try:
            # For MP3 files, we can try to estimate duration
            # This is a simple estimation - for more accurate duration,
            # you might want to use libraries like mutagen
            file_size = file_path.stat().st_size
            
            # Rough estimation based on file size and format
            if file_path.suffix.lower() == '.mp3':
                # Assume average bitrate of 128 kbps
                estimated_duration = (file_size * 8) / (128 * 1000)
            elif file_path.suffix.lower() == '.wav':
                # WAV files are uncompressed, roughly 1.4MB per minute at 44.1kHz stereo
                estimated_duration = file_size / (1.4 * 1024 * 1024 / 60)
            else:
                # Default estimation
                estimated_duration = file_size / (16 * 1024)  # Very rough estimate
            
            return max(1.0, estimated_duration)  # At least 1 second
            
        except Exception as e:
            logger.warning(f"Could not estimate audio duration: {e}")
            return 0.0
    
    def play(self, file_path: str = None) -> bool:
        """
        Play audio file
        
        Args:
            file_path: Path to audio file (optional if already loaded)
            
        Returns:
            True if playback started successfully, False otherwise
        """
        if not self.is_initialized:
            logger.error("Audio player not initialized")
            return False
        
        try:
            # Load file if provided
            if file_path:
                if not self.load(file_path):
                    return False
            
            if not self.current_file:
                logger.error("No audio file loaded")
                return False
            
            # Start playback
            pygame.mixer.music.set_volume(self.volume)
            pygame.mixer.music.play()
            
            self.is_playing_flag = True
            self.is_paused_flag = False
            self.position = 0
            
            # Start position tracking thread
            self._start_position_tracking()
            
            logger.info("Audio playback started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start audio playback: {e}")
            return False
    
    def pause(self):
        """Pause audio playback"""
        if not self.is_initialized or not self.is_playing_flag:
            return
        
        try:
            pygame.mixer.music.pause()
            self.is_paused_flag = True
            logger.info("Audio playback paused")
        except Exception as e:
            logger.error(f"Failed to pause audio: {e}")
    
    def resume(self):
        """Resume audio playback"""
        if not self.is_initialized or not self.is_paused_flag:
            return
        
        try:
            pygame.mixer.music.unpause()
            self.is_paused_flag = False
            logger.info("Audio playback resumed")
        except Exception as e:
            logger.error(f"Failed to resume audio: {e}")
    
    def stop(self):
        """Stop audio playback"""
        if not self.is_initialized:
            return
        
        try:
            pygame.mixer.music.stop()
            self.is_playing_flag = False
            self.is_paused_flag = False
            self.position = 0
            logger.info("Audio playback stopped")
        except Exception as e:
            logger.error(f"Failed to stop audio: {e}")
    
    def set_volume(self, volume: float):
        """
        Set playback volume
        
        Args:
            volume: Volume level (0.0 to 1.0)
        """
        self.volume = max(0.0, min(1.0, volume))
        if self.is_initialized and self.is_playing_flag:
            pygame.mixer.music.set_volume(self.volume)
    
    def get_volume(self) -> float:
        """Get current volume level"""
        return self.volume
    
    def is_playing(self) -> bool:
        """Check if audio is currently playing"""
        if not self.is_initialized:
            return False
        
        # Check pygame mixer status
        return pygame.mixer.music.get_busy() and not self.is_paused_flag
    
    def is_paused(self) -> bool:
        """Check if audio is paused"""
        return self.is_paused_flag
    
    def get_position(self) -> float:
        """Get current playback position in seconds"""
        return self.position
    
    def get_duration(self) -> float:
        """Get audio duration in seconds"""
        return self.duration
    
    def _start_position_tracking(self):
        """Start position tracking thread"""
        def track_position():
            start_time = time.time()
            
            while self.is_playing_flag and pygame.mixer.music.get_busy():
                if not self.is_paused_flag:
                    self.position = time.time() - start_time
                    
                    # Call position callback if set
                    if self.position_callback:
                        try:
                            self.position_callback(self.position, self.duration)
                        except Exception as e:
                            logger.error(f"Position callback error: {e}")
                
                time.sleep(0.1)  # Update every 100ms
            
            # Playback finished
            self.is_playing_flag = False
            self.is_paused_flag = False
            
            # Call finished callback if set
            if self.finished_callback:
                try:
                    self.finished_callback()
                except Exception as e:
                    logger.error(f"Finished callback error: {e}")
        
        # Start tracking thread
        tracking_thread = threading.Thread(target=track_position, daemon=True)
        tracking_thread.start()
    
    def cleanup(self):
        """Clean up resources"""
        try:
            self.stop()
            if self.is_initialized:
                pygame.mixer.quit()
            logger.info("Audio player cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Test function
def test_audio_player():
    """Test the audio player functionality"""
    player = AudioPlayer()
    
    def on_position_update(pos, duration):
        print(f"Position: {pos:.1f}s / {duration:.1f}s")
    
    def on_finished():
        print("Playback finished")
    
    player.set_callbacks(on_position_update, on_finished)
    
    # This would need an actual audio file to test
    # player.play("test_audio.mp3")

if __name__ == "__main__":
    test_audio_player()
