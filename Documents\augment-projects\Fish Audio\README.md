# Fish Audio TTS Application

A comprehensive Text-to-Speech (TTS) application that integrates with the Fish Audio API to convert text into natural-sounding speech with advanced voice control and customization options.

## Features

### Core Functionality
- **Text-to-Speech Conversion**: Convert text to high-quality speech using Fish Audio's advanced TTS models
- **Multiple Voice Models**: Support for various TTS models (speech-1.5, speech-1.6, s1)
- **Custom Voice Models**: Create and use custom voice models with reference audio
- **Real-time Generation**: Asynchronous speech generation with progress tracking

### Audio Controls
- **Playback Controls**: Play, pause, stop, and resume audio playback
- **Volume Control**: Adjustable volume levels
- **Speed Control**: Variable speech speed (0.5x to 2.0x)
- **Format Options**: Support for MP3, WAV, and Opus audio formats

### User Interface
- **Intuitive GUI**: Clean, user-friendly interface built with tkinter
- **Text Input Area**: Large text input with character count and validation
- **Parameter Controls**: Easy-to-use sliders and dropdowns for speech parameters
- **Progress Tracking**: Real-time progress bar during speech generation
- **Status Updates**: Clear status messages and error handling

### File Management
- **Save/Export**: Save generated audio files in various formats
- **Output Directory**: Configurable output directory for generated files
- **File Naming**: Automatic timestamped file naming

### Configuration
- **Settings Dialog**: Comprehensive settings for API configuration and preferences
- **API Key Management**: Secure API key storage and validation
- **Parameter Presets**: Save and load preferred TTS parameters

## Installation

### Prerequisites
- Python 3.7 or higher
- Fish Audio API key (get one at [fish.audio](https://fish.audio))

### Quick Setup (Recommended)
Run the setup script for automatic installation:
```bash
python setup.py
```

### Manual Installation

#### Step 1: Clone or Download
Download the application files to your local machine.

#### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
```

#### Step 3: Configuration
1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and add your Fish Audio API key:
   ```
   FISH_AUDIO_API_KEY=your_actual_api_key_here
   ```

#### Step 4: Run the Application
```bash
python main.py
```

## Configuration

### API Settings
- **API Key**: Your Fish Audio API key (required)
- **Base URL**: API endpoint (default: https://api.fish.audio)
- **Default Model**: Preferred TTS model (speech-1.5, speech-1.6, or s1)

### Audio Settings
- **Output Format**: Default audio format (MP3, WAV, Opus)
- **Sample Rate**: Audio sample rate (8kHz to 48kHz)
- **Bitrate**: Audio quality/compression settings

### Application Settings
- **Max Text Length**: Maximum characters per TTS request (default: 5000)
- **Window Size**: Default application window dimensions
- **Output Directory**: Where generated audio files are saved

## Usage

### Basic Text-to-Speech
1. **Enter Text**: Type or paste your text in the input area
2. **Select Model**: Choose a TTS model from the dropdown
3. **Adjust Parameters**: Set speed, volume, and format as desired
4. **Generate**: Click "Generate Speech" to create audio
5. **Play**: Use playback controls to listen to the generated speech
6. **Save**: Export the audio file to your desired location

### Advanced Features

#### Custom Voice Models
1. Go to Settings → API Settings
2. Use the Fish Audio web interface to upload reference audio
3. Note the model ID and use it in the application

#### Parameter Tuning
- **Speed**: 0.5 (slow) to 2.0 (fast)
- **Volume**: -10 (quiet) to +10 (loud)
- **Temperature**: Controls randomness in speech generation
- **Top-p**: Nucleus sampling parameter for voice variation

#### Batch Processing
For multiple texts, generate each one individually and save with descriptive names.

## Supported Audio Formats

### MP3
- Sample Rates: 32kHz, 44.1kHz
- Bitrates: 64, 128, 192 kbps
- Mono output

### WAV/PCM
- Sample Rates: 8kHz, 16kHz, 24kHz, 32kHz, 44.1kHz
- 16-bit, mono
- Uncompressed

### Opus
- Sample Rate: 48kHz
- Bitrates: 24, 32, 48, 64 kbps
- Mono output

## Troubleshooting

### Common Issues

#### "API key not configured"
- Ensure your `.env` file contains a valid `FISH_AUDIO_API_KEY`
- Check that the API key is correct and active
- Use the "Test Connection" button in Settings

#### "Connection failed"
- Verify internet connection
- Check if Fish Audio API is accessible
- Ensure firewall/proxy settings allow API access

#### "Audio playback failed"
- Install pygame: `pip install pygame`
- Check system audio settings
- Ensure audio file was generated successfully

#### "Generation failed"
- Check text length (must be under limit)
- Verify API key has sufficient credits
- Try a different TTS model

### Error Logs
Check `app.log` for detailed error information and debugging.

### Getting Help
1. Check the error message and status bar
2. Review the application log file
3. Verify your API key and internet connection
4. Try with shorter text or different parameters

## API Rate Limits

Fish Audio API has rate limits to ensure fair usage:
- Check your account dashboard for current limits
- The application will show appropriate error messages if limits are exceeded
- Consider upgrading your plan for higher limits

## Development

### Project Structure
```
fish-audio-tts/
├── main.py              # Application entry point
├── main_gui.py          # Main GUI application
├── fish_audio_client.py # Fish Audio API client
├── tts_engine.py        # Core TTS engine
├── audio_player.py      # Audio playback functionality
├── settings_dialog.py   # Settings configuration dialog
├── config.py            # Configuration management
├── requirements.txt     # Python dependencies
├── .env.example         # Environment variables template
└── README.md           # This file
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is provided as-is for educational and personal use. Please respect Fish Audio's terms of service when using their API.

## Acknowledgments

- Fish Audio for providing the excellent TTS API
- Python community for the amazing libraries used in this project
- Contributors and testers who helped improve the application

---

For more information about Fish Audio's TTS capabilities, visit [fish.audio](https://fish.audio).
