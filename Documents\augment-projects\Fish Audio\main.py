#!/usr/bin/env python3
"""
Fish Audio TTS Application
Main entry point for the Text-to-Speech application using Fish Audio API

Author: Fish Audio TTS Team
Version: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(current_dir / 'app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are installed"""
    missing_deps = []
    
    try:
        import requests
    except ImportError:
        missing_deps.append('requests')
    
    try:
        import msgpack
    except ImportError:
        missing_deps.append('msgpack')
    
    try:
        import pygame
    except ImportError:
        missing_deps.append('pygame')
    
    try:
        from dotenv import load_dotenv
    except ImportError:
        missing_deps.append('python-dotenv')
    
    if missing_deps:
        error_msg = f"Missing required dependencies: {', '.join(missing_deps)}\n\n"
        error_msg += "Please install them using:\n"
        error_msg += f"pip install {' '.join(missing_deps)}"
        
        # Show error in GUI if possible
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Missing Dependencies", error_msg)
            root.destroy()
        except:
            print(error_msg)
        
        return False
    
    return True

def check_configuration():
    """Check if configuration is valid"""
    try:
        from config import Config
        
        # Check if .env file exists
        env_file = current_dir / '.env'
        if not env_file.exists():
            logger.warning(".env file not found. Using default configuration.")
            return True
        
        # Check if API key is configured
        if not Config.validate_api_key():
            logger.warning("API key not configured. User will need to set it up.")
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        return False

def main():
    """Main application entry point"""
    logger.info("Starting Fish Audio TTS Application")
    
    try:
        # Check dependencies
        if not check_dependencies():
            logger.error("Dependency check failed")
            return 1
        
        # Check configuration
        if not check_configuration():
            logger.error("Configuration check failed")
            return 1
        
        # Import and start the main GUI application
        from main_gui import FishAudioTTSApp
        
        logger.info("Initializing GUI application")
        app = FishAudioTTSApp()
        
        logger.info("Starting application main loop")
        app.run()
        
        logger.info("Application closed normally")
        return 0
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        
        # Show error dialog if possible
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror(
                "Application Error",
                f"An unexpected error occurred:\n\n{str(e)}\n\nCheck the log file for more details."
            )
            root.destroy()
        except:
            print(f"Fatal error: {e}")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
