"""
Core TTS Engine Module
Handles text processing, audio generation, and file management
"""
import os
import io
import threading
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List
import logging
from datetime import datetime

from fish_audio_client import FishAudioClient, FishAudioAPIError
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TTSEngine:
    """Core TTS engine for handling text-to-speech operations"""
    
    def __init__(self, api_key: str = None):
        """
        Initialize TTS engine
        
        Args:
            api_key: Fish Audio API key
        """
        self.client = FishAudioClient(api_key)
        self.is_generating = False
        self.current_audio_data = None
        self.current_audio_path = None
        self.generation_thread = None
        self.progress_callback = None
        self.completion_callback = None
        self.error_callback = None
        
        # TTS parameters
        self.current_params = {
            'model': Config.FISH_AUDIO_DEFAULT_MODEL,
            'speed': Config.DEFAULT_SPEED,
            'volume': Config.DEFAULT_VOLUME,
            'temperature': Config.DEFAULT_TEMPERATURE,
            'top_p': Config.DEFAULT_TOP_P,
            'chunk_length': Config.DEFAULT_CHUNK_LENGTH,
            'format': Config.DEFAULT_OUTPUT_FORMAT,
            'sample_rate': Config.DEFAULT_SAMPLE_RATE,
            'mp3_bitrate': Config.DEFAULT_BITRATE,
            'normalize': True,
            'latency': 'normal'
        }
        
        # Available models cache
        self._models_cache = None
        self._models_cache_time = None
        self._cache_duration = 300  # 5 minutes
    
    def set_callbacks(
        self,
        progress_callback: Callable[[int], None] = None,
        completion_callback: Callable[[str], None] = None,
        error_callback: Callable[[str], None] = None
    ):
        """
        Set callback functions for TTS operations
        
        Args:
            progress_callback: Called with progress percentage (0-100)
            completion_callback: Called with output file path when complete
            error_callback: Called with error message if generation fails
        """
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
        self.error_callback = error_callback
    
    def update_parameters(self, **params):
        """
        Update TTS parameters
        
        Args:
            **params: TTS parameters to update
        """
        for key, value in params.items():
            if key in self.current_params:
                self.current_params[key] = value
            else:
                logger.warning(f"Unknown parameter: {key}")
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get current TTS parameters"""
        return self.current_params.copy()
    
    def validate_text(self, text: str) -> tuple[bool, str]:
        """
        Validate input text
        
        Args:
            text: Text to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not text or not text.strip():
            return False, "Text cannot be empty"
        
        if len(text) > Config.MAX_TEXT_LENGTH:
            return False, f"Text too long. Maximum length is {Config.MAX_TEXT_LENGTH} characters"
        
        # Check for potentially problematic characters
        if any(ord(char) > 65535 for char in text):
            return False, "Text contains unsupported characters"
        
        return True, ""
    
    def get_available_models(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Get list of available TTS models
        
        Args:
            force_refresh: Force refresh of models cache
            
        Returns:
            List of model dictionaries
        """
        current_time = time.time()
        
        # Check cache validity
        if (not force_refresh and 
            self._models_cache is not None and 
            self._models_cache_time is not None and
            current_time - self._models_cache_time < self._cache_duration):
            return self._models_cache
        
        try:
            models = self.client.list_models()
            self._models_cache = models
            self._models_cache_time = current_time
            return models
        except Exception as e:
            logger.error(f"Failed to fetch models: {e}")
            # Return default models if API call fails
            default_models = [
                {'id': model, 'name': model.title(), 'type': 'tts'} 
                for model in Config.AVAILABLE_MODELS
            ]
            return default_models
    
    def test_connection(self) -> tuple[bool, str]:
        """
        Test connection to Fish Audio API
        
        Returns:
            Tuple of (is_connected, message)
        """
        try:
            if self.client.test_connection():
                return True, "Connection successful"
            else:
                return False, "Connection failed - please check your API key"
        except Exception as e:
            return False, f"Connection error: {str(e)}"
    
    def generate_speech_async(
        self,
        text: str,
        output_filename: str = None,
        reference_id: str = None,
        **params
    ):
        """
        Generate speech asynchronously
        
        Args:
            text: Text to convert to speech
            output_filename: Output file name (optional)
            reference_id: Reference model ID (optional)
            **params: Additional TTS parameters
        """
        if self.is_generating:
            if self.error_callback:
                self.error_callback("Speech generation already in progress")
            return
        
        # Validate text
        is_valid, error_msg = self.validate_text(text)
        if not is_valid:
            if self.error_callback:
                self.error_callback(error_msg)
            return
        
        # Update parameters
        generation_params = self.current_params.copy()
        generation_params.update(params)
        
        # Generate output filename if not provided
        if not output_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            format_ext = generation_params.get('format', 'mp3')
            output_filename = f"tts_output_{timestamp}.{format_ext}"
        
        # Start generation in separate thread
        self.generation_thread = threading.Thread(
            target=self._generate_speech_worker,
            args=(text, output_filename, reference_id, generation_params),
            daemon=True
        )
        self.generation_thread.start()
    
    def _generate_speech_worker(
        self,
        text: str,
        output_filename: str,
        reference_id: str,
        params: Dict[str, Any]
    ):
        """
        Worker method for speech generation
        
        Args:
            text: Text to convert
            output_filename: Output file name
            reference_id: Reference model ID
            params: TTS parameters
        """
        self.is_generating = True
        
        try:
            # Prepare output path
            output_path = Config.OUTPUT_DIR / output_filename
            
            # Initialize progress
            if self.progress_callback:
                self.progress_callback(0)
            
            # Generate speech
            audio_chunks = []
            total_chunks = 0
            processed_chunks = 0
            
            logger.info(f"Starting TTS generation for text: {text[:50]}...")
            
            # Get audio stream from API
            for chunk in self.client.text_to_speech(
                text=text,
                reference_id=reference_id,
                **params
            ):
                audio_chunks.append(chunk)
                processed_chunks += 1
                
                # Update progress (estimate based on text length)
                estimated_total = max(10, len(text) // 20)  # Rough estimate
                progress = min(90, int((processed_chunks / estimated_total) * 90))
                
                if self.progress_callback:
                    self.progress_callback(progress)
            
            # Combine audio chunks
            if self.progress_callback:
                self.progress_callback(95)
            
            audio_data = b''.join(audio_chunks)
            
            # Save to file
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            # Store current audio data and path
            self.current_audio_data = audio_data
            self.current_audio_path = str(output_path)
            
            logger.info(f"TTS generation completed: {output_path}")
            
            # Final progress update
            if self.progress_callback:
                self.progress_callback(100)
            
            # Call completion callback
            if self.completion_callback:
                self.completion_callback(str(output_path))
                
        except FishAudioAPIError as e:
            error_msg = f"API Error: {e.message}"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
                
        except Exception as e:
            error_msg = f"Generation failed: {str(e)}"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
        
        finally:
            self.is_generating = False
    
    def cancel_generation(self):
        """Cancel ongoing speech generation"""
        if self.is_generating and self.generation_thread:
            # Note: This is a simple cancellation - in a more robust implementation,
            # you might want to use threading.Event for proper cancellation
            self.is_generating = False
            logger.info("Speech generation cancelled")
    
    def get_current_audio_path(self) -> Optional[str]:
        """Get path to the most recently generated audio file"""
        return self.current_audio_path
    
    def get_current_audio_data(self) -> Optional[bytes]:
        """Get the most recently generated audio data"""
        return self.current_audio_data
