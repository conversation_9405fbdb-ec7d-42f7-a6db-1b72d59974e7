"""
Configuration module for Fish Audio TTS Application
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Application configuration class"""
    
    # API Configuration
    FISH_AUDIO_API_KEY = os.getenv('FISH_AUDIO_API_KEY', '')
    FISH_AUDIO_BASE_URL = os.getenv('FISH_AUDIO_BASE_URL', 'https://api.fish.audio')
    FISH_AUDIO_DEFAULT_MODEL = os.getenv('FISH_AUDIO_DEFAULT_MODEL', 'speech-1.6')
    
    # Available TTS models
    AVAILABLE_MODELS = [
        'speech-1.5',
        'speech-1.6', 
        's1'
    ]
    
    # Audio Configuration
    DEFAULT_OUTPUT_FORMAT = os.getenv('DEFAULT_OUTPUT_FORMAT', 'mp3')
    DEFAULT_SAMPLE_RATE = int(os.getenv('DEFAULT_SAMPLE_RATE', '44100'))
    DEFAULT_BITRATE = int(os.getenv('DEFAULT_BITRATE', '128'))
    MAX_TEXT_LENGTH = int(os.getenv('MAX_TEXT_LENGTH', '5000'))
    
    # TTS Parameters
    DEFAULT_SPEED = float(os.getenv('DEFAULT_SPEED', '1.0'))
    DEFAULT_VOLUME = float(os.getenv('DEFAULT_VOLUME', '0.0'))
    DEFAULT_CHUNK_LENGTH = int(os.getenv('DEFAULT_CHUNK_LENGTH', '200'))
    DEFAULT_TEMPERATURE = 0.7
    DEFAULT_TOP_P = 0.7
    
    # Audio format options
    SUPPORTED_FORMATS = ['mp3', 'wav', 'opus']
    SUPPORTED_SAMPLE_RATES = [8000, 16000, 24000, 32000, 44100, 48000]
    SUPPORTED_BITRATES = {
        'mp3': [64, 128, 192],
        'opus': [24, 32, 48, 64]
    }
    
    # UI Configuration
    WINDOW_WIDTH = int(os.getenv('WINDOW_WIDTH', '800'))
    WINDOW_HEIGHT = int(os.getenv('WINDOW_HEIGHT', '600'))
    THEME = os.getenv('THEME', 'light')
    
    # File paths
    BASE_DIR = Path(__file__).parent
    OUTPUT_DIR = BASE_DIR / 'output'
    TEMP_DIR = BASE_DIR / 'temp'
    
    # Ensure directories exist
    OUTPUT_DIR.mkdir(exist_ok=True)
    TEMP_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def validate_api_key(cls) -> bool:
        """Validate if API key is configured"""
        return bool(cls.FISH_AUDIO_API_KEY and cls.FISH_AUDIO_API_KEY != 'your_api_key_here')
    
    @classmethod
    def get_headers(cls) -> dict:
        """Get API headers with authentication"""
        return {
            'Authorization': f'Bearer {cls.FISH_AUDIO_API_KEY}',
            'Content-Type': 'application/json'
        }
    
    @classmethod
    def get_msgpack_headers(cls, model: str = None) -> dict:
        """Get headers for MessagePack requests"""
        headers = {
            'Authorization': f'Bearer {cls.FISH_AUDIO_API_KEY}',
            'Content-Type': 'application/msgpack'
        }
        if model:
            headers['model'] = model
        return headers
