"""
Main GUI Application for Fish Audio TTS
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import os
from pathlib import Path
from typing import Optional

from tts_engine import TTSEngine
from config import Config
from audio_player import AudioPlayer
from settings_dialog import SettingsDialog

class FishAudioTTSApp:
    """Main application class for Fish Audio TTS GUI"""
    
    def __init__(self):
        """Initialize the application"""
        self.root = tk.Tk()
        self.root.title("Fish Audio TTS - Text to Speech Application")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        self.root.minsize(600, 400)
        
        # Initialize components
        self.tts_engine = None
        self.audio_player = AudioPlayer()
        self.current_audio_file = None
        
        # GUI variables
        self.text_var = tk.StringVar()
        self.char_count_var = tk.StringVar(value="0 / 5000")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready")
        self.model_var = tk.StringVar()
        self.speed_var = tk.DoubleVar(value=Config.DEFAULT_SPEED)
        self.volume_var = tk.DoubleVar(value=Config.DEFAULT_VOLUME)
        self.format_var = tk.StringVar(value=Config.DEFAULT_OUTPUT_FORMAT)
        
        # Initialize TTS engine
        self.init_tts_engine()
        
        # Create GUI
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        # Load available models
        self.load_models()
    
    def init_tts_engine(self):
        """Initialize TTS engine with error handling"""
        try:
            if not Config.validate_api_key():
                messagebox.showerror(
                    "Configuration Error",
                    "API key not configured. Please set up your Fish Audio API key in the settings."
                )
                self.show_settings()
                return
            
            self.tts_engine = TTSEngine()
            self.tts_engine.set_callbacks(
                progress_callback=self.on_progress_update,
                completion_callback=self.on_generation_complete,
                error_callback=self.on_generation_error
            )
            
            # Test connection
            is_connected, message = self.tts_engine.test_connection()
            if not is_connected:
                messagebox.showwarning("Connection Warning", message)
                
        except Exception as e:
            messagebox.showerror("Initialization Error", f"Failed to initialize TTS engine: {str(e)}")
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main container
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # Text input section
        self.text_frame = ttk.LabelFrame(self.main_frame, text="Text Input", padding="5")
        
        self.text_area = scrolledtext.ScrolledText(
            self.text_frame,
            height=8,
            width=60,
            wrap=tk.WORD,
            font=("Arial", 11)
        )
        
        self.char_count_label = ttk.Label(self.text_frame, textvariable=self.char_count_var)
        
        # Controls section
        self.controls_frame = ttk.LabelFrame(self.main_frame, text="Controls", padding="5")
        
        # Model selection
        ttk.Label(self.controls_frame, text="Model:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.model_combo = ttk.Combobox(
            self.controls_frame,
            textvariable=self.model_var,
            state="readonly",
            width=20
        )
        
        # Speed control
        ttk.Label(self.controls_frame, text="Speed:").grid(row=0, column=2, sticky="w", padx=(20, 5))
        self.speed_scale = ttk.Scale(
            self.controls_frame,
            from_=0.5,
            to=2.0,
            variable=self.speed_var,
            orient="horizontal",
            length=100
        )
        self.speed_label = ttk.Label(self.controls_frame, text="1.0")
        
        # Volume control
        ttk.Label(self.controls_frame, text="Volume:").grid(row=1, column=0, sticky="w", padx=(0, 5))
        self.volume_scale = ttk.Scale(
            self.controls_frame,
            from_=-10,
            to=10,
            variable=self.volume_var,
            orient="horizontal",
            length=100
        )
        self.volume_label = ttk.Label(self.controls_frame, text="0.0")
        
        # Format selection
        ttk.Label(self.controls_frame, text="Format:").grid(row=1, column=2, sticky="w", padx=(20, 5))
        self.format_combo = ttk.Combobox(
            self.controls_frame,
            textvariable=self.format_var,
            values=Config.SUPPORTED_FORMATS,
            state="readonly",
            width=10
        )
        
        # Action buttons
        self.buttons_frame = ttk.Frame(self.controls_frame)
        
        self.generate_btn = ttk.Button(
            self.buttons_frame,
            text="Generate Speech",
            command=self.generate_speech,
            style="Accent.TButton"
        )
        
        self.stop_btn = ttk.Button(
            self.buttons_frame,
            text="Stop",
            command=self.stop_generation,
            state="disabled"
        )
        
        self.settings_btn = ttk.Button(
            self.buttons_frame,
            text="Settings",
            command=self.show_settings
        )
        
        # Audio playback section
        self.audio_frame = ttk.LabelFrame(self.main_frame, text="Audio Playback", padding="5")
        
        self.play_btn = ttk.Button(
            self.audio_frame,
            text="▶ Play",
            command=self.play_audio,
            state="disabled"
        )
        
        self.pause_btn = ttk.Button(
            self.audio_frame,
            text="⏸ Pause",
            command=self.pause_audio,
            state="disabled"
        )
        
        self.stop_audio_btn = ttk.Button(
            self.audio_frame,
            text="⏹ Stop",
            command=self.stop_audio,
            state="disabled"
        )
        
        self.save_btn = ttk.Button(
            self.audio_frame,
            text="💾 Save As...",
            command=self.save_audio,
            state="disabled"
        )
        
        # Progress section
        self.progress_frame = ttk.Frame(self.main_frame)
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=400
        )
        
        self.status_label = ttk.Label(self.progress_frame, textvariable=self.status_var)
    
    def setup_layout(self):
        """Setup widget layout using grid"""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure root grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # Configure main frame grid weights
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Text input section
        self.text_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        self.text_area.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        self.char_count_label.grid(row=1, column=0, sticky="e")
        
        self.text_frame.grid_columnconfigure(0, weight=1)
        
        # Controls section
        self.controls_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        # Model and speed row
        self.model_combo.grid(row=0, column=1, sticky="w", padx=(0, 20))
        self.speed_scale.grid(row=0, column=3, sticky="w", padx=(0, 5))
        self.speed_label.grid(row=0, column=4, sticky="w")
        
        # Volume and format row
        self.volume_scale.grid(row=1, column=1, sticky="w", padx=(0, 5))
        self.volume_label.grid(row=1, column=1, sticky="e", padx=(110, 20))
        self.format_combo.grid(row=1, column=3, sticky="w")
        
        # Buttons
        self.buttons_frame.grid(row=2, column=0, columnspan=5, pady=(10, 0))
        self.generate_btn.pack(side="left", padx=(0, 5))
        self.stop_btn.pack(side="left", padx=(0, 5))
        self.settings_btn.pack(side="left", padx=(0, 5))
        
        # Audio playback section
        self.audio_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        
        self.play_btn.grid(row=0, column=0, padx=(0, 5))
        self.pause_btn.grid(row=0, column=1, padx=(0, 5))
        self.stop_audio_btn.grid(row=0, column=2, padx=(0, 5))
        self.save_btn.grid(row=0, column=3, padx=(20, 0))
        
        # Progress section
        self.progress_frame.grid(row=3, column=0, sticky="ew")
        self.progress_bar.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        self.status_label.grid(row=1, column=0, sticky="w")
        
        self.progress_frame.grid_columnconfigure(0, weight=1)
    
    def bind_events(self):
        """Bind event handlers"""
        # Text area events
        self.text_area.bind('<KeyRelease>', self.on_text_change)
        self.text_area.bind('<Button-1>', self.on_text_change)

        # Scale events
        self.speed_var.trace('w', self.on_speed_change)
        self.volume_var.trace('w', self.on_volume_change)

        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_models(self):
        """Load available TTS models"""
        if not self.tts_engine:
            return

        try:
            models = self.tts_engine.get_available_models()
            model_names = [model.get('id', model.get('name', 'Unknown')) for model in models]

            self.model_combo['values'] = model_names
            if model_names:
                self.model_var.set(Config.FISH_AUDIO_DEFAULT_MODEL if Config.FISH_AUDIO_DEFAULT_MODEL in model_names else model_names[0])
        except Exception as e:
            print(f"Failed to load models: {e}")
            # Set default models
            self.model_combo['values'] = Config.AVAILABLE_MODELS
            self.model_var.set(Config.FISH_AUDIO_DEFAULT_MODEL)

    def on_text_change(self, event=None):
        """Handle text area changes"""
        text = self.text_area.get("1.0", tk.END).strip()
        char_count = len(text)
        self.char_count_var.set(f"{char_count} / {Config.MAX_TEXT_LENGTH}")

        # Change color if approaching limit
        if char_count > Config.MAX_TEXT_LENGTH * 0.9:
            self.char_count_label.configure(foreground="red")
        elif char_count > Config.MAX_TEXT_LENGTH * 0.8:
            self.char_count_label.configure(foreground="orange")
        else:
            self.char_count_label.configure(foreground="black")

    def on_speed_change(self, *args):
        """Handle speed scale changes"""
        speed = round(self.speed_var.get(), 1)
        self.speed_label.configure(text=str(speed))

    def on_volume_change(self, *args):
        """Handle volume scale changes"""
        volume = round(self.volume_var.get(), 1)
        self.volume_label.configure(text=str(volume))

    def generate_speech(self):
        """Generate speech from text"""
        if not self.tts_engine:
            messagebox.showerror("Error", "TTS engine not initialized")
            return

        text = self.text_area.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("Warning", "Please enter some text to convert")
            return

        # Update UI state
        self.generate_btn.configure(state="disabled")
        self.stop_btn.configure(state="normal")
        self.progress_var.set(0)
        self.status_var.set("Generating speech...")

        # Update TTS parameters
        self.tts_engine.update_parameters(
            model=self.model_var.get(),
            speed=self.speed_var.get(),
            volume=self.volume_var.get(),
            format=self.format_var.get()
        )

        # Start generation
        self.tts_engine.generate_speech_async(text)

    def stop_generation(self):
        """Stop speech generation"""
        if self.tts_engine:
            self.tts_engine.cancel_generation()

        self.generate_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.progress_var.set(0)
        self.status_var.set("Generation stopped")

    def on_progress_update(self, progress: int):
        """Handle progress updates from TTS engine"""
        self.root.after(0, lambda: self.progress_var.set(progress))

    def on_generation_complete(self, audio_file_path: str):
        """Handle completion of speech generation"""
        def update_ui():
            self.current_audio_file = audio_file_path
            self.generate_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.progress_var.set(100)
            self.status_var.set(f"Generation complete: {Path(audio_file_path).name}")

            # Enable audio controls
            self.play_btn.configure(state="normal")
            self.save_btn.configure(state="normal")

            # Auto-play if desired (could be a setting)
            # self.play_audio()

        self.root.after(0, update_ui)

    def on_generation_error(self, error_message: str):
        """Handle generation errors"""
        def update_ui():
            self.generate_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.progress_var.set(0)
            self.status_var.set("Generation failed")
            messagebox.showerror("Generation Error", error_message)

        self.root.after(0, update_ui)

    def play_audio(self):
        """Play generated audio"""
        if not self.current_audio_file:
            messagebox.showwarning("Warning", "No audio file to play")
            return

        try:
            self.audio_player.play(self.current_audio_file)
            self.play_btn.configure(state="disabled")
            self.pause_btn.configure(state="normal")
            self.stop_audio_btn.configure(state="normal")
            self.status_var.set("Playing audio...")
        except Exception as e:
            messagebox.showerror("Playback Error", f"Failed to play audio: {str(e)}")

    def pause_audio(self):
        """Pause audio playback"""
        try:
            if self.audio_player.is_playing():
                self.audio_player.pause()
                self.play_btn.configure(state="normal")
                self.pause_btn.configure(state="disabled")
                self.status_var.set("Audio paused")
            else:
                self.audio_player.resume()
                self.play_btn.configure(state="disabled")
                self.pause_btn.configure(state="normal")
                self.status_var.set("Playing audio...")
        except Exception as e:
            messagebox.showerror("Playback Error", f"Failed to pause/resume audio: {str(e)}")

    def stop_audio(self):
        """Stop audio playback"""
        try:
            self.audio_player.stop()
            self.play_btn.configure(state="normal")
            self.pause_btn.configure(state="disabled")
            self.stop_audio_btn.configure(state="disabled")
            self.status_var.set("Audio stopped")
        except Exception as e:
            messagebox.showerror("Playback Error", f"Failed to stop audio: {str(e)}")

    def save_audio(self):
        """Save audio file to chosen location"""
        if not self.current_audio_file:
            messagebox.showwarning("Warning", "No audio file to save")
            return

        # Get file extension
        current_path = Path(self.current_audio_file)
        default_ext = current_path.suffix

        # File dialog
        filetypes = [
            ("MP3 files", "*.mp3"),
            ("WAV files", "*.wav"),
            ("Opus files", "*.opus"),
            ("All files", "*.*")
        ]

        save_path = filedialog.asksaveasfilename(
            title="Save Audio File",
            defaultextension=default_ext,
            filetypes=filetypes,
            initialname=f"fish_audio_tts{default_ext}"
        )

        if save_path:
            try:
                import shutil
                shutil.copy2(self.current_audio_file, save_path)
                messagebox.showinfo("Success", f"Audio saved to: {save_path}")
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save audio: {str(e)}")

    def show_settings(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self.root)
        if dialog.result:
            # Reinitialize TTS engine if API key changed
            self.init_tts_engine()
            self.load_models()

    def on_closing(self):
        """Handle application closing"""
        # Stop any ongoing operations
        if self.tts_engine and self.tts_engine.is_generating:
            self.tts_engine.cancel_generation()

        if self.audio_player:
            self.audio_player.cleanup()

        self.root.destroy()

    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    app = FishAudioTTSApp()
    app.run()

if __name__ == "__main__":
    main()
