#!/usr/bin/env python3
"""
Fish Audio TTS Application - Test Suite
Comprehensive testing of application components
"""

import sys
import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

class TestConfig(unittest.TestCase):
    """Test configuration module"""
    
    def test_config_import(self):
        """Test that config can be imported"""
        from config import Config
        self.assertIsNotNone(Config)
    
    def test_config_attributes(self):
        """Test that config has required attributes"""
        from config import Config
        
        required_attrs = [
            'FISH_AUDIO_API_KEY',
            'FISH_AUDIO_BASE_URL',
            'AVAILABLE_MODELS',
            'DEFAULT_OUTPUT_FORMAT',
            'SUPPORTED_FORMATS'
        ]
        
        for attr in required_attrs:
            self.assertTrue(hasattr(Config, attr), f"Config missing attribute: {attr}")
    
    def test_validate_api_key(self):
        """Test API key validation"""
        from config import Config
        
        # Should have validate_api_key method
        self.assertTrue(hasattr(Config, 'validate_api_key'))
        self.assertTrue(callable(Config.validate_api_key))

class TestFishAudioClient(unittest.TestCase):
    """Test Fish Audio API client"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_api_key = "test_api_key_12345"
    
    def test_client_initialization(self):
        """Test client initialization"""
        from fish_audio_client import FishAudioClient
        
        client = FishAudioClient(self.test_api_key)
        self.assertEqual(client.api_key, self.test_api_key)
        self.assertIsNotNone(client.session)
    
    def test_client_without_api_key(self):
        """Test client initialization without API key"""
        from fish_audio_client import FishAudioClient, FishAudioAPIError
        
        with patch('config.Config.FISH_AUDIO_API_KEY', ''):
            with self.assertRaises(FishAudioAPIError):
                FishAudioClient()
    
    @patch('requests.Session.request')
    def test_make_request_success(self, mock_request):
        """Test successful API request"""
        from fish_audio_client import FishAudioClient
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        mock_request.return_value = mock_response
        
        client = FishAudioClient(self.test_api_key)
        response = client._make_request('GET', '/test')
        
        self.assertEqual(response.status_code, 200)
    
    @patch('requests.Session.request')
    def test_make_request_error(self, mock_request):
        """Test API request error handling"""
        from fish_audio_client import FishAudioClient, FishAudioAPIError
        
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_request.return_value = mock_response
        
        client = FishAudioClient(self.test_api_key)
        
        with self.assertRaises(FishAudioAPIError):
            client._make_request('GET', '/test')

class TestTTSEngine(unittest.TestCase):
    """Test TTS engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_api_key = "test_api_key_12345"
    
    @patch('fish_audio_client.FishAudioClient')
    def test_engine_initialization(self, mock_client_class):
        """Test TTS engine initialization"""
        from tts_engine import TTSEngine
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        engine = TTSEngine(self.test_api_key)
        
        self.assertIsNotNone(engine.client)
        self.assertFalse(engine.is_generating)
        self.assertIsNotNone(engine.current_params)
    
    def test_validate_text(self):
        """Test text validation"""
        from tts_engine import TTSEngine
        
        with patch('fish_audio_client.FishAudioClient'):
            engine = TTSEngine(self.test_api_key)
            
            # Test empty text
            is_valid, error = engine.validate_text("")
            self.assertFalse(is_valid)
            self.assertIn("empty", error.lower())
            
            # Test valid text
            is_valid, error = engine.validate_text("Hello world")
            self.assertTrue(is_valid)
            self.assertEqual(error, "")
            
            # Test very long text
            long_text = "A" * 10000
            is_valid, error = engine.validate_text(long_text)
            self.assertFalse(is_valid)
            self.assertIn("long", error.lower())
    
    def test_parameter_updates(self):
        """Test parameter updates"""
        from tts_engine import TTSEngine
        
        with patch('fish_audio_client.FishAudioClient'):
            engine = TTSEngine(self.test_api_key)
            
            # Update parameters
            engine.update_parameters(speed=1.5, volume=2.0)
            
            params = engine.get_parameters()
            self.assertEqual(params['speed'], 1.5)
            self.assertEqual(params['volume'], 2.0)

class TestAudioPlayer(unittest.TestCase):
    """Test audio player"""
    
    @patch('pygame.mixer.init')
    @patch('pygame.mixer.pre_init')
    def test_player_initialization(self, mock_pre_init, mock_init):
        """Test audio player initialization"""
        from audio_player import AudioPlayer
        
        player = AudioPlayer()
        
        mock_pre_init.assert_called_once()
        mock_init.assert_called_once()
    
    @patch('pygame.mixer.init')
    @patch('pygame.mixer.pre_init')
    def test_player_methods(self, mock_pre_init, mock_init):
        """Test audio player methods exist"""
        from audio_player import AudioPlayer
        
        player = AudioPlayer()
        
        # Check that required methods exist
        required_methods = ['play', 'pause', 'resume', 'stop', 'set_volume', 'is_playing']
        
        for method in required_methods:
            self.assertTrue(hasattr(player, method))
            self.assertTrue(callable(getattr(player, method)))

class TestFileManager(unittest.TestCase):
    """Test file manager"""
    
    def test_file_manager_initialization(self):
        """Test file manager initialization"""
        from file_manager import FileManager
        
        manager = FileManager()
        
        self.assertIsNotNone(manager.output_dir)
        self.assertIsNotNone(manager.temp_dir)
    
    def test_generate_filename(self):
        """Test filename generation"""
        from file_manager import FileManager
        
        manager = FileManager()
        
        # Test basic filename generation
        filename = manager.generate_filename(
            text="Hello world",
            format="mp3",
            prefix="test",
            include_timestamp=False
        )
        
        self.assertIn("test", filename)
        self.assertIn("hello", filename.lower())
        self.assertTrue(filename.endswith(".mp3"))
    
    def test_sanitize_filename(self):
        """Test filename sanitization"""
        from file_manager import FileManager
        
        manager = FileManager()
        
        # Test with invalid characters
        dirty_filename = "test<>:\"/\\|?*.mp3"
        clean_filename = manager.sanitize_filename(dirty_filename)
        
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            self.assertNotIn(char, clean_filename)
    
    def test_save_audio_file(self):
        """Test audio file saving"""
        from file_manager import FileManager
        
        manager = FileManager()
        
        # Test with temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Test data
            test_data = b"fake audio data"
            
            result = manager.save_audio_file(
                test_data,
                "test.mp3",
                temp_path
            )
            
            self.assertIsNotNone(result)
            self.assertTrue(Path(result).exists())

class TestErrorHandler(unittest.TestCase):
    """Test error handler"""
    
    def test_error_handler_initialization(self):
        """Test error handler initialization"""
        from error_handler import ErrorHandler
        
        with tempfile.NamedTemporaryFile(suffix='.log', delete=False) as temp_log:
            handler = ErrorHandler(temp_log.name)
            
            self.assertIsNotNone(handler.logger)
            self.assertEqual(handler.log_file, Path(temp_log.name))
            
            # Cleanup
            os.unlink(temp_log.name)
    
    def test_convenience_functions(self):
        """Test convenience functions"""
        from error_handler import log_error, log_warning, log_info
        
        # These should not raise exceptions
        log_error("Test error")
        log_warning("Test warning")
        log_info("Test info")

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_import_all_modules(self):
        """Test that all modules can be imported"""
        modules = [
            'config',
            'fish_audio_client',
            'tts_engine',
            'audio_player',
            'file_manager',
            'error_handler',
            'settings_dialog',
            'main_gui'
        ]
        
        for module_name in modules:
            try:
                __import__(module_name)
            except ImportError as e:
                self.fail(f"Failed to import {module_name}: {e}")
    
    def test_main_application_creation(self):
        """Test that main application can be created"""
        with patch('tkinter.Tk'):
            with patch('config.Config.validate_api_key', return_value=True):
                with patch('fish_audio_client.FishAudioClient'):
                    from main_gui import FishAudioTTSApp
                    
                    # This should not raise an exception
                    app = FishAudioTTSApp()
                    self.assertIsNotNone(app)

def run_tests():
    """Run all tests"""
    print("Fish Audio TTS Application - Test Suite")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestConfig,
        TestFishAudioClient,
        TestTTSEngine,
        TestAudioPlayer,
        TestFileManager,
        TestErrorHandler,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall: {'✅ PASSED' if success else '❌ FAILED'}")
    
    return success

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
