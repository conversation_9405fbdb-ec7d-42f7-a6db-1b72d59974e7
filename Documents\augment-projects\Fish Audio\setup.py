#!/usr/bin/env python3
"""
Setup script for Fish Audio TTS Application
Handles installation and initial configuration
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """Print setup header"""
    print("=" * 60)
    print("Fish Audio TTS Application Setup")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\nInstalling dependencies...")
    
    try:
        # Check if pip is available
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        
        # Install requirements
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies:")
            print(result.stderr)
            return False
            
    except subprocess.CalledProcessError:
        print("❌ pip is not available. Please install pip first.")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def setup_configuration():
    """Setup configuration file"""
    print("\nSetting up configuration...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    # Copy .env.example to .env if it doesn't exist
    if not env_file.exists():
        try:
            shutil.copy2(env_example, env_file)
            print("✅ Created .env configuration file")
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    else:
        print("ℹ️  .env file already exists")
    
    return True

def get_api_key():
    """Get API key from user"""
    print("\nAPI Key Configuration")
    print("-" * 30)
    print("You need a Fish Audio API key to use this application.")
    print("Get your API key at: https://fish.audio")
    print()
    
    while True:
        api_key = input("Enter your Fish Audio API key (or press Enter to skip): ").strip()
        
        if not api_key:
            print("⚠️  Skipping API key setup. You can configure it later in Settings.")
            return None
        
        if len(api_key) < 10:
            print("❌ API key seems too short. Please check and try again.")
            continue
        
        return api_key

def update_env_file(api_key):
    """Update .env file with API key"""
    if not api_key:
        return True
    
    try:
        env_file = Path(".env")
        
        # Read current content
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
        else:
            content = ""
        
        # Update API key
        lines = content.split('\n')
        updated = False
        
        for i, line in enumerate(lines):
            if line.startswith('FISH_AUDIO_API_KEY='):
                lines[i] = f'FISH_AUDIO_API_KEY={api_key}'
                updated = True
                break
        
        if not updated:
            lines.append(f'FISH_AUDIO_API_KEY={api_key}')
        
        # Write back
        with open(env_file, 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ API key configured successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False

def test_installation():
    """Test if installation is working"""
    print("\nTesting installation...")
    
    try:
        # Try importing main modules
        import requests
        import msgpack
        import pygame
        from dotenv import load_dotenv
        
        print("✅ All dependencies imported successfully")
        
        # Try loading configuration
        sys.path.insert(0, str(Path.cwd()))
        from config import Config
        
        print("✅ Configuration loaded successfully")
        
        if Config.validate_api_key():
            print("✅ API key configured and valid")
        else:
            print("⚠️  API key not configured or invalid")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    
    try:
        directories = ['output', 'temp']
        
        for dir_name in directories:
            dir_path = Path(dir_name)
            dir_path.mkdir(exist_ok=True)
            print(f"✅ Created directory: {dir_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create directories: {e}")
        return False

def print_completion_message():
    """Print setup completion message"""
    print("\n" + "=" * 60)
    print("Setup Complete!")
    print("=" * 60)
    print()
    print("To start the application, run:")
    print("  python main.py")
    print()
    print("If you skipped API key setup, you can configure it later:")
    print("1. Run the application")
    print("2. Click 'Settings' button")
    print("3. Enter your API key in the API Settings tab")
    print()
    print("For help and documentation, see README.md")
    print()

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        return 1
    
    # Setup configuration
    if not setup_configuration():
        print("\n❌ Setup failed during configuration")
        return 1
    
    # Create directories
    if not create_directories():
        print("\n❌ Setup failed during directory creation")
        return 1
    
    # Get API key
    api_key = get_api_key()
    
    # Update .env file
    if not update_env_file(api_key):
        print("\n❌ Setup failed during API key configuration")
        return 1
    
    # Test installation
    if not test_installation():
        print("\n❌ Setup completed with warnings")
        print("The application may not work correctly.")
        return 1
    
    # Print completion message
    print_completion_message()
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)
