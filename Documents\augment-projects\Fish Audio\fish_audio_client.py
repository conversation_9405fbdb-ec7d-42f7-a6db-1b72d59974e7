"""
Fish Audio API Client
Handles all interactions with the Fish Audio TTS API
"""
import requests
import msgpack
import json
import time
from typing import Dict, List, Optional, Union, Generator, Any
from pathlib import Path
import logging
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FishAudioAPIError(Exception):
    """Custom exception for Fish Audio API errors"""
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)

class FishAudioClient:
    """Fish Audio API client for TTS operations"""
    
    def __init__(self, api_key: str = None):
        """
        Initialize the Fish Audio client
        
        Args:
            api_key: Fish Audio API key. If not provided, will use config.
        """
        self.api_key = api_key or Config.FISH_AUDIO_API_KEY
        self.base_url = Config.FISH_AUDIO_BASE_URL
        self.session = requests.Session()
        
        if not self.api_key:
            raise FishAudioAPIError("API key is required. Please set FISH_AUDIO_API_KEY in your .env file.")
        
        # Set default headers
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'User-Agent': 'FishAudio-TTS-App/1.0'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make HTTP request with error handling
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Response object
            
        Raises:
            FishAudioAPIError: If request fails
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # Handle different error status codes
            if response.status_code == 401:
                raise FishAudioAPIError(
                    "Authentication failed. Please check your API key.",
                    status_code=401
                )
            elif response.status_code == 402:
                raise FishAudioAPIError(
                    "Payment required. Please check your account balance.",
                    status_code=402
                )
            elif response.status_code == 422:
                try:
                    error_data = response.json()
                    error_msg = "Validation error: " + str(error_data)
                except:
                    error_msg = "Validation error occurred"
                raise FishAudioAPIError(error_msg, status_code=422, response_data=error_data)
            elif response.status_code >= 400:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('message', f'HTTP {response.status_code} error')
                except:
                    error_msg = f'HTTP {response.status_code} error'
                raise FishAudioAPIError(error_msg, status_code=response.status_code)
            
            return response
            
        except requests.exceptions.ConnectionError:
            raise FishAudioAPIError("Connection error. Please check your internet connection.")
        except requests.exceptions.Timeout:
            raise FishAudioAPIError("Request timeout. Please try again.")
        except requests.exceptions.RequestException as e:
            raise FishAudioAPIError(f"Request failed: {str(e)}")
    
    def test_connection(self) -> bool:
        """
        Test API connection and authentication
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Try to get models list as a connection test
            self.list_models()
            return True
        except FishAudioAPIError:
            return False
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available models

        Returns:
            List of model information dictionaries
        """
        try:
            response = self._make_request('GET', '/model')
            return response.json()
        except Exception as e:
            logger.warning(f"Could not fetch models list: {e}")
            # Return default models if API call fails
            return [
                {'id': model, 'name': model.title(), 'type': 'tts'}
                for model in Config.AVAILABLE_MODELS
            ]

    def text_to_speech(
        self,
        text: str,
        model: str = None,
        reference_id: str = None,
        references: List[Dict[str, Any]] = None,
        speed: float = 1.0,
        volume: float = 0.0,
        temperature: float = 0.7,
        top_p: float = 0.7,
        chunk_length: int = 200,
        format: str = 'mp3',
        sample_rate: int = None,
        mp3_bitrate: int = 128,
        opus_bitrate: int = 32,
        normalize: bool = True,
        latency: str = 'normal'
    ) -> Generator[bytes, None, None]:
        """
        Convert text to speech using Fish Audio API

        Args:
            text: Text to convert to speech
            model: TTS model to use
            reference_id: ID of uploaded reference model
            references: List of reference audio data
            speed: Speech speed (0.5-2.0)
            volume: Volume adjustment (-10 to 10)
            temperature: Randomness in generation (0.0-1.0)
            top_p: Nucleus sampling parameter (0.0-1.0)
            chunk_length: Audio chunk length (100-300)
            format: Output format ('mp3', 'wav', 'opus')
            sample_rate: Sample rate (optional)
            mp3_bitrate: MP3 bitrate (64, 128, 192)
            opus_bitrate: Opus bitrate (24, 32, 48, 64)
            normalize: Whether to normalize text
            latency: Latency mode ('normal', 'balanced')

        Yields:
            Audio data chunks as bytes

        Raises:
            FishAudioAPIError: If TTS request fails
        """
        if not text.strip():
            raise FishAudioAPIError("Text cannot be empty")

        if len(text) > Config.MAX_TEXT_LENGTH:
            raise FishAudioAPIError(f"Text too long. Maximum length is {Config.MAX_TEXT_LENGTH} characters")

        # Prepare request data
        request_data = {
            'text': text,
            'temperature': temperature,
            'top_p': top_p,
            'chunk_length': chunk_length,
            'normalize': normalize,
            'format': format,
            'latency': latency,
            'prosody': {
                'speed': speed,
                'volume': volume
            }
        }

        # Add format-specific parameters
        if format == 'mp3':
            request_data['mp3_bitrate'] = mp3_bitrate
        elif format == 'opus':
            request_data['opus_bitrate'] = opus_bitrate

        if sample_rate:
            request_data['sample_rate'] = sample_rate

        # Add reference audio or reference ID
        if reference_id:
            request_data['reference_id'] = reference_id
        elif references:
            request_data['references'] = references

        # Prepare headers
        headers = {'Content-Type': 'application/json'}
        if model:
            headers['model'] = model

        try:
            # Make streaming request
            response = self._make_request(
                'POST',
                '/v1/tts',
                json=request_data,
                headers=headers,
                stream=True,
                timeout=30
            )

            # Stream the response
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    yield chunk

        except Exception as e:
            raise FishAudioAPIError(f"TTS request failed: {str(e)}")

    def create_model(
        self,
        name: str,
        audio_file: Union[str, Path, bytes],
        text: str,
        description: str = None,
        tags: List[str] = None
    ) -> Dict[str, Any]:
        """
        Create a custom voice model by uploading reference audio

        Args:
            name: Model name
            audio_file: Audio file path, Path object, or audio bytes
            text: Text corresponding to the audio
            description: Model description
            tags: List of tags for the model

        Returns:
            Model creation response data

        Raises:
            FishAudioAPIError: If model creation fails
        """
        try:
            # Prepare form data
            files = {}
            data = {
                'name': name,
                'text': text
            }

            if description:
                data['description'] = description
            if tags:
                data['tags'] = ','.join(tags)

            # Handle audio file
            if isinstance(audio_file, (str, Path)):
                audio_path = Path(audio_file)
                if not audio_path.exists():
                    raise FishAudioAPIError(f"Audio file not found: {audio_path}")

                with open(audio_path, 'rb') as f:
                    files['audio'] = (audio_path.name, f.read(), 'audio/wav')
            elif isinstance(audio_file, bytes):
                files['audio'] = ('audio.wav', audio_file, 'audio/wav')
            else:
                raise FishAudioAPIError("Invalid audio_file type. Must be path string, Path object, or bytes")

            # Make request
            response = self._make_request(
                'POST',
                '/model',
                data=data,
                files=files,
                headers={'Authorization': f'Bearer {self.api_key}'}  # Override content-type for multipart
            )

            return response.json()

        except Exception as e:
            raise FishAudioAPIError(f"Model creation failed: {str(e)}")

    def get_model(self, model_id: str) -> Dict[str, Any]:
        """
        Get information about a specific model

        Args:
            model_id: Model ID

        Returns:
            Model information
        """
        response = self._make_request('GET', f'/model/{model_id}')
        return response.json()

    def delete_model(self, model_id: str) -> bool:
        """
        Delete a custom model

        Args:
            model_id: Model ID to delete

        Returns:
            True if deletion successful
        """
        try:
            self._make_request('DELETE', f'/model/{model_id}')
            return True
        except FishAudioAPIError:
            return False
