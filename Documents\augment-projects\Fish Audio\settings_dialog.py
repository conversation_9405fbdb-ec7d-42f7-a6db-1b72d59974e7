"""
Settings Dialog for Fish Audio TTS Application
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from pathlib import Path
from config import Config

class SettingsDialog:
    """Settings dialog for configuring application preferences"""
    
    def __init__(self, parent):
        """
        Initialize settings dialog
        
        Args:
            parent: Parent window
        """
        self.parent = parent
        self.result = False
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Settings - Fish Audio TTS")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.center_dialog()
        
        # Variables for settings
        self.api_key_var = tk.StringVar(value=Config.FISH_AUDIO_API_KEY)
        self.base_url_var = tk.StringVar(value=Config.FISH_AUDIO_BASE_URL)
        self.default_model_var = tk.StringVar(value=Config.FISH_AUDIO_DEFAULT_MODEL)
        self.output_format_var = tk.StringVar(value=Config.DEFAULT_OUTPUT_FORMAT)
        self.sample_rate_var = tk.StringVar(value=str(Config.DEFAULT_SAMPLE_RATE))
        self.bitrate_var = tk.StringVar(value=str(Config.DEFAULT_BITRATE))
        self.max_text_length_var = tk.StringVar(value=str(Config.MAX_TEXT_LENGTH))
        self.window_width_var = tk.StringVar(value=str(Config.WINDOW_WIDTH))
        self.window_height_var = tk.StringVar(value=str(Config.WINDOW_HEIGHT))
        
        # Create widgets
        self.create_widgets()
        
        # Bind events
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
        self.dialog.bind('<Return>', lambda e: self.on_save())
        self.dialog.bind('<Escape>', lambda e: self.on_cancel())
        
        # Focus on API key field
        self.api_key_entry.focus_set()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def center_dialog(self):
        """Center the dialog on the parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get dialog size
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()
        
        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main container
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill="both", expand=True)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill="both", expand=True, pady=(0, 20))
        
        # API Settings Tab
        api_frame = ttk.Frame(notebook, padding="10")
        notebook.add(api_frame, text="API Settings")
        
        # API Key
        ttk.Label(api_frame, text="Fish Audio API Key:").grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.api_key_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, width=50, show="*")
        self.api_key_entry.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # Show/Hide API key button
        self.show_api_key_btn = ttk.Button(
            api_frame, 
            text="Show", 
            command=self.toggle_api_key_visibility,
            width=8
        )
        self.show_api_key_btn.grid(row=1, column=2, padx=(5, 0), pady=(0, 10))
        
        # Base URL
        ttk.Label(api_frame, text="API Base URL:").grid(row=2, column=0, sticky="w", pady=(0, 5))
        ttk.Entry(api_frame, textvariable=self.base_url_var, width=50).grid(row=3, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # Default Model
        ttk.Label(api_frame, text="Default Model:").grid(row=4, column=0, sticky="w", pady=(0, 5))
        model_combo = ttk.Combobox(
            api_frame, 
            textvariable=self.default_model_var,
            values=Config.AVAILABLE_MODELS,
            state="readonly",
            width=20
        )
        model_combo.grid(row=5, column=0, sticky="w", pady=(0, 10))
        
        # Test Connection Button
        test_btn = ttk.Button(api_frame, text="Test Connection", command=self.test_connection)
        test_btn.grid(row=6, column=0, pady=(10, 0))
        
        api_frame.grid_columnconfigure(0, weight=1)
        
        # Audio Settings Tab
        audio_frame = ttk.Frame(notebook, padding="10")
        notebook.add(audio_frame, text="Audio Settings")
        
        # Output Format
        ttk.Label(audio_frame, text="Default Output Format:").grid(row=0, column=0, sticky="w", pady=(0, 5))
        format_combo = ttk.Combobox(
            audio_frame,
            textvariable=self.output_format_var,
            values=Config.SUPPORTED_FORMATS,
            state="readonly",
            width=15
        )
        format_combo.grid(row=1, column=0, sticky="w", pady=(0, 10))
        
        # Sample Rate
        ttk.Label(audio_frame, text="Default Sample Rate:").grid(row=2, column=0, sticky="w", pady=(0, 5))
        sample_rate_combo = ttk.Combobox(
            audio_frame,
            textvariable=self.sample_rate_var,
            values=[str(rate) for rate in Config.SUPPORTED_SAMPLE_RATES],
            state="readonly",
            width=15
        )
        sample_rate_combo.grid(row=3, column=0, sticky="w", pady=(0, 10))
        
        # Bitrate
        ttk.Label(audio_frame, text="Default MP3 Bitrate:").grid(row=4, column=0, sticky="w", pady=(0, 5))
        bitrate_combo = ttk.Combobox(
            audio_frame,
            textvariable=self.bitrate_var,
            values=[str(rate) for rate in Config.SUPPORTED_BITRATES['mp3']],
            state="readonly",
            width=15
        )
        bitrate_combo.grid(row=5, column=0, sticky="w", pady=(0, 10))
        
        # Application Settings Tab
        app_frame = ttk.Frame(notebook, padding="10")
        notebook.add(app_frame, text="Application")
        
        # Max Text Length
        ttk.Label(app_frame, text="Maximum Text Length:").grid(row=0, column=0, sticky="w", pady=(0, 5))
        ttk.Entry(app_frame, textvariable=self.max_text_length_var, width=15).grid(row=1, column=0, sticky="w", pady=(0, 10))
        
        # Window Size
        ttk.Label(app_frame, text="Default Window Size:").grid(row=2, column=0, sticky="w", pady=(0, 5))
        
        size_frame = ttk.Frame(app_frame)
        size_frame.grid(row=3, column=0, sticky="w", pady=(0, 10))
        
        ttk.Entry(size_frame, textvariable=self.window_width_var, width=8).pack(side="left")
        ttk.Label(size_frame, text=" x ").pack(side="left")
        ttk.Entry(size_frame, textvariable=self.window_height_var, width=8).pack(side="left")
        
        # Output Directory
        ttk.Label(app_frame, text="Output Directory:").grid(row=4, column=0, sticky="w", pady=(10, 5))
        
        dir_frame = ttk.Frame(app_frame)
        dir_frame.grid(row=5, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        self.output_dir_var = tk.StringVar(value=str(Config.OUTPUT_DIR))
        ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=40).pack(side="left", fill="x", expand=True)
        ttk.Button(dir_frame, text="Browse", command=self.browse_output_dir).pack(side="right", padx=(5, 0))
        
        dir_frame.grid_columnconfigure(0, weight=1)
        app_frame.grid_columnconfigure(0, weight=1)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x")
        
        # Cancel and Save buttons
        ttk.Button(buttons_frame, text="Cancel", command=self.on_cancel).pack(side="right", padx=(5, 0))
        ttk.Button(buttons_frame, text="Save", command=self.on_save).pack(side="right")
        ttk.Button(buttons_frame, text="Reset to Defaults", command=self.reset_defaults).pack(side="left")
    
    def toggle_api_key_visibility(self):
        """Toggle API key visibility"""
        if self.api_key_entry['show'] == '*':
            self.api_key_entry.configure(show='')
            self.show_api_key_btn.configure(text="Hide")
        else:
            self.api_key_entry.configure(show='*')
            self.show_api_key_btn.configure(text="Show")
    
    def test_connection(self):
        """Test API connection"""
        api_key = self.api_key_var.get().strip()
        if not api_key:
            messagebox.showwarning("Warning", "Please enter an API key first")
            return
        
        try:
            # Import here to avoid circular imports
            from fish_audio_client import FishAudioClient
            
            client = FishAudioClient(api_key)
            if client.test_connection():
                messagebox.showinfo("Success", "Connection successful!")
            else:
                messagebox.showerror("Error", "Connection failed. Please check your API key.")
        except Exception as e:
            messagebox.showerror("Error", f"Connection test failed: {str(e)}")
    
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(
            title="Select Output Directory",
            initialdir=self.output_dir_var.get()
        )
        if directory:
            self.output_dir_var.set(directory)
    
    def reset_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Confirm", "Reset all settings to defaults?"):
            self.api_key_var.set("")
            self.base_url_var.set("https://api.fish.audio")
            self.default_model_var.set("speech-1.6")
            self.output_format_var.set("mp3")
            self.sample_rate_var.set("44100")
            self.bitrate_var.set("128")
            self.max_text_length_var.set("5000")
            self.window_width_var.set("800")
            self.window_height_var.set("600")
            self.output_dir_var.set(str(Config.BASE_DIR / 'output'))
    
    def validate_settings(self) -> tuple[bool, str]:
        """
        Validate settings
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Validate API key
        if not self.api_key_var.get().strip():
            return False, "API key is required"
        
        # Validate numeric fields
        try:
            sample_rate = int(self.sample_rate_var.get())
            if sample_rate not in Config.SUPPORTED_SAMPLE_RATES:
                return False, f"Invalid sample rate. Supported rates: {Config.SUPPORTED_SAMPLE_RATES}"
        except ValueError:
            return False, "Sample rate must be a number"
        
        try:
            bitrate = int(self.bitrate_var.get())
            if bitrate not in Config.SUPPORTED_BITRATES['mp3']:
                return False, f"Invalid bitrate. Supported bitrates: {Config.SUPPORTED_BITRATES['mp3']}"
        except ValueError:
            return False, "Bitrate must be a number"
        
        try:
            max_length = int(self.max_text_length_var.get())
            if max_length < 1 or max_length > 50000:
                return False, "Max text length must be between 1 and 50000"
        except ValueError:
            return False, "Max text length must be a number"
        
        try:
            width = int(self.window_width_var.get())
            height = int(self.window_height_var.get())
            if width < 400 or height < 300:
                return False, "Window size too small (minimum 400x300)"
        except ValueError:
            return False, "Window dimensions must be numbers"
        
        return True, ""
    
    def save_settings(self):
        """Save settings to .env file"""
        try:
            env_path = Config.BASE_DIR / '.env'
            
            # Read existing .env file or create new one
            env_content = {}
            if env_path.exists():
                with open(env_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            env_content[key.strip()] = value.strip()
            
            # Update with new values
            env_content.update({
                'FISH_AUDIO_API_KEY': self.api_key_var.get().strip(),
                'FISH_AUDIO_BASE_URL': self.base_url_var.get().strip(),
                'FISH_AUDIO_DEFAULT_MODEL': self.default_model_var.get(),
                'DEFAULT_OUTPUT_FORMAT': self.output_format_var.get(),
                'DEFAULT_SAMPLE_RATE': self.sample_rate_var.get(),
                'DEFAULT_BITRATE': self.bitrate_var.get(),
                'MAX_TEXT_LENGTH': self.max_text_length_var.get(),
                'WINDOW_WIDTH': self.window_width_var.get(),
                'WINDOW_HEIGHT': self.window_height_var.get()
            })
            
            # Write back to file
            with open(env_path, 'w') as f:
                f.write("# Fish Audio TTS Application Configuration\n")
                f.write("# Generated by settings dialog\n\n")
                for key, value in env_content.items():
                    f.write(f"{key}={value}\n")
            
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
            return False
    
    def on_save(self):
        """Handle save button click"""
        # Validate settings
        is_valid, error_msg = self.validate_settings()
        if not is_valid:
            messagebox.showerror("Validation Error", error_msg)
            return
        
        # Save settings
        if self.save_settings():
            self.result = True
            self.dialog.destroy()
    
    def on_cancel(self):
        """Handle cancel button click"""
        self.result = False
        self.dialog.destroy()
