#!/usr/bin/env python3
"""
Fish Audio TTS Application - Example Usage <PERSON>ts
Demonstrates various features and usage patterns
"""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from fish_audio_client import FishAudioClient, FishAudioAPIError
from tts_engine import TTSEngine
from config import Config
from error_handler import log_info, log_error, handle_exception

def example_basic_tts():
    """Example: Basic text-to-speech conversion"""
    print("=" * 50)
    print("Example 1: Basic Text-to-Speech")
    print("=" * 50)
    
    try:
        # Initialize TTS engine
        engine = TTSEngine()
        
        # Test connection
        is_connected, message = engine.test_connection()
        if not is_connected:
            print(f"❌ Connection failed: {message}")
            return
        
        print("✅ Connected to Fish Audio API")
        
        # Set up callbacks
        def on_progress(progress):
            print(f"Progress: {progress}%")
        
        def on_complete(audio_file):
            print(f"✅ Speech generated: {audio_file}")
        
        def on_error(error_msg):
            print(f"❌ Error: {error_msg}")
        
        engine.set_callbacks(on_progress, on_complete, on_error)
        
        # Generate speech
        text = "Hello! This is a test of the Fish Audio TTS system. It sounds quite natural, doesn't it?"
        print(f"Converting text: {text}")
        
        engine.generate_speech_async(text)
        
        # Wait for completion (in real app, this would be handled by GUI)
        while engine.is_generating:
            time.sleep(0.1)
        
        print("Example completed!")
        
    except Exception as e:
        handle_exception(e, "Basic TTS Example")

def example_custom_parameters():
    """Example: Using custom TTS parameters"""
    print("\n" + "=" * 50)
    print("Example 2: Custom Parameters")
    print("=" * 50)
    
    try:
        engine = TTSEngine()
        
        # Update parameters
        engine.update_parameters(
            speed=1.2,  # Slightly faster
            volume=2.0,  # Slightly louder
            format='wav',  # WAV format
            temperature=0.8,  # More variation
            chunk_length=150  # Smaller chunks
        )
        
        print("Parameters updated:")
        params = engine.get_parameters()
        for key, value in params.items():
            print(f"  {key}: {value}")
        
        # Generate with custom parameters
        text = "This speech uses custom parameters for speed, volume, and format."
        
        def on_complete(audio_file):
            print(f"✅ Custom speech generated: {audio_file}")
        
        engine.set_callbacks(completion_callback=on_complete)
        engine.generate_speech_async(text, output_filename="custom_params_example.wav")
        
        # Wait for completion
        while engine.is_generating:
            time.sleep(0.1)
        
        print("Custom parameters example completed!")
        
    except Exception as e:
        handle_exception(e, "Custom Parameters Example")

def example_api_client_direct():
    """Example: Using API client directly"""
    print("\n" + "=" * 50)
    print("Example 3: Direct API Client Usage")
    print("=" * 50)
    
    try:
        # Initialize client
        client = FishAudioClient()
        
        # Test connection
        if not client.test_connection():
            print("❌ API connection failed")
            return
        
        print("✅ API client connected")
        
        # Get available models
        models = client.list_models()
        print(f"Available models: {len(models)}")
        for model in models[:3]:  # Show first 3
            print(f"  - {model.get('id', model.get('name', 'Unknown'))}")
        
        # Generate speech directly
        text = "This is generated using the API client directly."
        print(f"Generating: {text}")
        
        audio_chunks = []
        for chunk in client.text_to_speech(
            text=text,
            model="speech-1.6",
            speed=1.0,
            format="mp3"
        ):
            audio_chunks.append(chunk)
        
        # Save audio
        audio_data = b''.join(audio_chunks)
        output_file = Config.OUTPUT_DIR / "direct_api_example.mp3"
        
        with open(output_file, 'wb') as f:
            f.write(audio_data)
        
        print(f"✅ Audio saved: {output_file}")
        print("Direct API example completed!")
        
    except FishAudioAPIError as e:
        print(f"❌ API Error: {e.message}")
    except Exception as e:
        handle_exception(e, "Direct API Example")

def example_error_handling():
    """Example: Error handling scenarios"""
    print("\n" + "=" * 50)
    print("Example 4: Error Handling")
    print("=" * 50)
    
    try:
        engine = TTSEngine()
        
        # Test 1: Empty text
        print("Test 1: Empty text")
        is_valid, error = engine.validate_text("")
        print(f"  Valid: {is_valid}, Error: {error}")
        
        # Test 2: Very long text
        print("Test 2: Very long text")
        long_text = "A" * (Config.MAX_TEXT_LENGTH + 100)
        is_valid, error = engine.validate_text(long_text)
        print(f"  Valid: {is_valid}, Error: {error}")
        
        # Test 3: Valid text
        print("Test 3: Valid text")
        is_valid, error = engine.validate_text("This is valid text.")
        print(f"  Valid: {is_valid}, Error: {error}")
        
        # Test 4: Connection test
        print("Test 4: Connection test")
        is_connected, message = engine.test_connection()
        print(f"  Connected: {is_connected}, Message: {message}")
        
        print("Error handling examples completed!")
        
    except Exception as e:
        handle_exception(e, "Error Handling Example")

def example_file_management():
    """Example: File management operations"""
    print("\n" + "=" * 50)
    print("Example 5: File Management")
    print("=" * 50)
    
    try:
        from file_manager import file_manager
        
        # Generate a filename
        filename = file_manager.generate_filename(
            text="Hello world example",
            format="mp3",
            prefix="demo"
        )
        print(f"Generated filename: {filename}")
        
        # List existing audio files
        audio_files = file_manager.list_audio_files()
        print(f"Found {len(audio_files)} audio files:")
        
        for file_info in audio_files[:3]:  # Show first 3
            print(f"  - {file_info['name']} ({file_info['size_mb']} MB)")
        
        # Get disk usage
        usage = file_manager.get_disk_usage()
        print(f"Disk usage: {usage['total_size_mb']} MB, {usage['file_count']} files")
        
        # Cleanup old temp files
        file_manager.cleanup_temp_files(max_age_hours=1)
        
        print("File management example completed!")
        
    except Exception as e:
        handle_exception(e, "File Management Example")

def example_batch_generation():
    """Example: Batch text-to-speech generation"""
    print("\n" + "=" * 50)
    print("Example 6: Batch Generation")
    print("=" * 50)
    
    try:
        engine = TTSEngine()
        
        # List of texts to convert
        texts = [
            "Welcome to our service.",
            "Please hold while we connect you.",
            "Thank you for your patience.",
            "Have a great day!"
        ]
        
        print(f"Generating {len(texts)} audio files...")
        
        completed_files = []
        
        def on_complete(audio_file):
            completed_files.append(audio_file)
            print(f"✅ Completed: {Path(audio_file).name}")
        
        engine.set_callbacks(completion_callback=on_complete)
        
        # Generate each text
        for i, text in enumerate(texts):
            filename = f"batch_example_{i+1}.mp3"
            print(f"Generating {i+1}/{len(texts)}: {text}")
            
            engine.generate_speech_async(text, output_filename=filename)
            
            # Wait for completion
            while engine.is_generating:
                time.sleep(0.1)
        
        print(f"Batch generation completed! Generated {len(completed_files)} files.")
        
    except Exception as e:
        handle_exception(e, "Batch Generation Example")

def run_all_examples():
    """Run all examples"""
    print("Fish Audio TTS Application - Examples")
    print("=" * 60)
    
    # Check if API key is configured
    if not Config.validate_api_key():
        print("❌ API key not configured!")
        print("Please set your Fish Audio API key in the .env file")
        print("Copy .env.example to .env and add your API key")
        return
    
    print("✅ API key configured")
    print()
    
    # Run examples
    examples = [
        example_basic_tts,
        example_custom_parameters,
        example_api_client_direct,
        example_error_handling,
        example_file_management,
        example_batch_generation
    ]
    
    for example_func in examples:
        try:
            example_func()
        except KeyboardInterrupt:
            print("\n❌ Examples interrupted by user")
            break
        except Exception as e:
            print(f"❌ Example failed: {e}")
            continue
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print("Check the 'output' directory for generated audio files.")

if __name__ == "__main__":
    try:
        run_all_examples()
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
