"""
File Management Module
Handles file operations, export functionality, and format conversions
"""

import os
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any
import json
from datetime import datetime
import logging

from config import Config
from error_handler import log_error, log_info, log_warning

logger = logging.getLogger(__name__)

class FileManager:
    """File management utilities for the TTS application"""
    
    def __init__(self):
        """Initialize file manager"""
        self.output_dir = Config.OUTPUT_DIR
        self.temp_dir = Config.TEMP_DIR
        
        # Ensure directories exist
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
    
    def generate_filename(
        self,
        text: str = None,
        format: str = "mp3",
        prefix: str = "tts",
        include_timestamp: bool = True
    ) -> str:
        """
        Generate a filename for audio output
        
        Args:
            text: Source text (used for naming)
            format: Audio format extension
            prefix: Filename prefix
            include_timestamp: Whether to include timestamp
            
        Returns:
            Generated filename
        """
        # Create base name
        if text and len(text) > 0:
            # Use first few words of text for filename
            words = text.strip().split()[:3]
            text_part = "_".join(word.lower() for word in words if word.isalnum())
            text_part = text_part[:30]  # Limit length
        else:
            text_part = "output"
        
        # Add timestamp if requested
        if include_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{prefix}_{text_part}_{timestamp}.{format}"
        else:
            filename = f"{prefix}_{text_part}.{format}"
        
        # Ensure filename is valid
        filename = self.sanitize_filename(filename)
        
        return filename
    
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to remove invalid characters
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove multiple underscores
        while '__' in filename:
            filename = filename.replace('__', '_')
        
        # Ensure filename is not too long
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        
        return filename
    
    def save_audio_file(
        self,
        audio_data: bytes,
        filename: str = None,
        output_dir: Path = None
    ) -> Optional[str]:
        """
        Save audio data to file
        
        Args:
            audio_data: Audio data as bytes
            filename: Output filename (optional)
            output_dir: Output directory (optional)
            
        Returns:
            Path to saved file or None if failed
        """
        try:
            if not audio_data:
                log_error("No audio data to save")
                return None
            
            # Use default output directory if not specified
            if output_dir is None:
                output_dir = self.output_dir
            
            # Generate filename if not provided
            if filename is None:
                filename = self.generate_filename()
            
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Create full path
            output_path = output_dir / filename
            
            # Save file
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            log_info(f"Audio saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            log_error(f"Failed to save audio file: {str(e)}", e)
            return None
    
    def copy_file(self, source_path: str, destination_path: str) -> bool:
        """
        Copy file from source to destination
        
        Args:
            source_path: Source file path
            destination_path: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            source = Path(source_path)
            destination = Path(destination_path)
            
            if not source.exists():
                log_error(f"Source file not found: {source}")
                return False
            
            # Create destination directory if needed
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            shutil.copy2(source, destination)
            
            log_info(f"File copied: {source} -> {destination}")
            return True
            
        except Exception as e:
            log_error(f"Failed to copy file: {str(e)}", e)
            return False
    
    def move_file(self, source_path: str, destination_path: str) -> bool:
        """
        Move file from source to destination
        
        Args:
            source_path: Source file path
            destination_path: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            source = Path(source_path)
            destination = Path(destination_path)
            
            if not source.exists():
                log_error(f"Source file not found: {source}")
                return False
            
            # Create destination directory if needed
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(str(source), str(destination))
            
            log_info(f"File moved: {source} -> {destination}")
            return True
            
        except Exception as e:
            log_error(f"Failed to move file: {str(e)}", e)
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                log_warning(f"File not found for deletion: {path}")
                return True  # Consider it successful if file doesn't exist
            
            path.unlink()
            log_info(f"File deleted: {path}")
            return True
            
        except Exception as e:
            log_error(f"Failed to delete file: {str(e)}", e)
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a file
        
        Args:
            file_path: Path to file
            
        Returns:
            Dictionary with file information or None if failed
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                return None
            
            stat = path.stat()
            
            return {
                'name': path.name,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'extension': path.suffix.lower(),
                'full_path': str(path.absolute())
            }
            
        except Exception as e:
            log_error(f"Failed to get file info: {str(e)}", e)
            return None
    
    def list_audio_files(self, directory: Path = None) -> List[Dict[str, Any]]:
        """
        List audio files in directory
        
        Args:
            directory: Directory to search (default: output directory)
            
        Returns:
            List of file information dictionaries
        """
        if directory is None:
            directory = self.output_dir
        
        audio_extensions = {'.mp3', '.wav', '.opus', '.m4a', '.aac', '.flac'}
        files = []
        
        try:
            if not directory.exists():
                return files
            
            for file_path in directory.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in audio_extensions:
                    file_info = self.get_file_info(str(file_path))
                    if file_info:
                        files.append(file_info)
            
            # Sort by modification time (newest first)
            files.sort(key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            log_error(f"Failed to list audio files: {str(e)}", e)
        
        return files
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        Clean up old temporary files
        
        Args:
            max_age_hours: Maximum age of temp files to keep (in hours)
        """
        try:
            if not self.temp_dir.exists():
                return
            
            current_time = datetime.now()
            cutoff_time = current_time.timestamp() - (max_age_hours * 3600)
            
            deleted_count = 0
            
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    try:
                        if file_path.stat().st_mtime < cutoff_time:
                            file_path.unlink()
                            deleted_count += 1
                    except Exception as e:
                        log_warning(f"Failed to delete temp file {file_path}: {e}")
            
            if deleted_count > 0:
                log_info(f"Cleaned up {deleted_count} temporary files")
                
        except Exception as e:
            log_error(f"Failed to cleanup temp files: {str(e)}", e)
    
    def get_disk_usage(self, directory: Path = None) -> Dict[str, float]:
        """
        Get disk usage information for directory
        
        Args:
            directory: Directory to check (default: output directory)
            
        Returns:
            Dictionary with usage information in MB
        """
        if directory is None:
            directory = self.output_dir
        
        try:
            total_size = 0
            file_count = 0
            
            if directory.exists():
                for file_path in directory.rglob('*'):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                        file_count += 1
            
            return {
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'file_count': file_count,
                'directory': str(directory)
            }
            
        except Exception as e:
            log_error(f"Failed to get disk usage: {str(e)}", e)
            return {'total_size_mb': 0, 'file_count': 0, 'directory': str(directory)}
    
    def export_file_list(self, output_file: str = None) -> Optional[str]:
        """
        Export list of generated files to JSON
        
        Args:
            output_file: Output file path (optional)
            
        Returns:
            Path to exported file or None if failed
        """
        try:
            if output_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = str(self.output_dir / f"file_list_{timestamp}.json")
            
            files = self.list_audio_files()
            
            # Convert datetime objects to strings for JSON serialization
            for file_info in files:
                file_info['created'] = file_info['created'].isoformat()
                file_info['modified'] = file_info['modified'].isoformat()
            
            export_data = {
                'export_time': datetime.now().isoformat(),
                'total_files': len(files),
                'files': files
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            log_info(f"File list exported to: {output_file}")
            return output_file
            
        except Exception as e:
            log_error(f"Failed to export file list: {str(e)}", e)
            return None

# Global file manager instance
file_manager = FileManager()
