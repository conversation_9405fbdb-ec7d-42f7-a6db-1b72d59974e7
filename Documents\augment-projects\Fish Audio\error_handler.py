"""
Error Handling and User Feedback Module
Provides comprehensive error handling, logging, and user notifications
"""

import logging
import traceback
import tkinter as tk
from tkinter import messagebox
from typing import Optional, Callable, Any
from pathlib import Path
import sys
from datetime import datetime

class ErrorHandler:
    """Centralized error handling and user feedback system"""
    
    def __init__(self, log_file: str = "app.log"):
        """
        Initialize error handler
        
        Args:
            log_file: Path to log file
        """
        self.log_file = Path(log_file)
        self.setup_logging()
        self.error_callbacks = []
        self.warning_callbacks = []
        self.info_callbacks = []
    
    def setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        self.log_file.parent.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Error handler initialized")
    
    def add_callback(self, level: str, callback: Callable[[str, str], None]):
        """
        Add callback for error notifications
        
        Args:
            level: Error level ('error', 'warning', 'info')
            callback: Callback function that takes (title, message)
        """
        if level == 'error':
            self.error_callbacks.append(callback)
        elif level == 'warning':
            self.warning_callbacks.append(callback)
        elif level == 'info':
            self.info_callbacks.append(callback)
    
    def handle_exception(
        self,
        exception: Exception,
        context: str = "Application",
        show_dialog: bool = True,
        log_traceback: bool = True
    ) -> str:
        """
        Handle an exception with logging and user notification
        
        Args:
            exception: The exception to handle
            context: Context where the exception occurred
            show_dialog: Whether to show error dialog to user
            log_traceback: Whether to log full traceback
            
        Returns:
            Error message string
        """
        error_msg = f"{context}: {str(exception)}"
        
        # Log the error
        if log_traceback:
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
        else:
            self.logger.error(error_msg)
        
        # Show dialog if requested
        if show_dialog:
            self.show_error_dialog("Error", error_msg)
        
        # Call error callbacks
        for callback in self.error_callbacks:
            try:
                callback("Error", error_msg)
            except Exception as e:
                self.logger.error(f"Error callback failed: {e}")
        
        return error_msg
    
    def log_error(self, message: str, exception: Exception = None, show_dialog: bool = False):
        """
        Log an error message
        
        Args:
            message: Error message
            exception: Optional exception object
            show_dialog: Whether to show error dialog
        """
        if exception:
            full_message = f"{message}: {str(exception)}"
            self.logger.error(f"{full_message}\n{traceback.format_exc()}")
        else:
            full_message = message
            self.logger.error(full_message)
        
        if show_dialog:
            self.show_error_dialog("Error", full_message)
        
        # Call error callbacks
        for callback in self.error_callbacks:
            try:
                callback("Error", full_message)
            except Exception as e:
                self.logger.error(f"Error callback failed: {e}")
    
    def log_warning(self, message: str, show_dialog: bool = False):
        """
        Log a warning message
        
        Args:
            message: Warning message
            show_dialog: Whether to show warning dialog
        """
        self.logger.warning(message)
        
        if show_dialog:
            self.show_warning_dialog("Warning", message)
        
        # Call warning callbacks
        for callback in self.warning_callbacks:
            try:
                callback("Warning", message)
            except Exception as e:
                self.logger.error(f"Warning callback failed: {e}")
    
    def log_info(self, message: str, show_dialog: bool = False):
        """
        Log an info message
        
        Args:
            message: Info message
            show_dialog: Whether to show info dialog
        """
        self.logger.info(message)
        
        if show_dialog:
            self.show_info_dialog("Information", message)
        
        # Call info callbacks
        for callback in self.info_callbacks:
            try:
                callback("Information", message)
            except Exception as e:
                self.logger.error(f"Info callback failed: {e}")
    
    def show_error_dialog(self, title: str, message: str, parent: tk.Widget = None):
        """
        Show error dialog to user
        
        Args:
            title: Dialog title
            message: Error message
            parent: Parent widget (optional)
        """
        try:
            messagebox.showerror(title, message, parent=parent)
        except Exception as e:
            print(f"Failed to show error dialog: {e}")
            print(f"Original error: {message}")
    
    def show_warning_dialog(self, title: str, message: str, parent: tk.Widget = None):
        """
        Show warning dialog to user
        
        Args:
            title: Dialog title
            message: Warning message
            parent: Parent widget (optional)
        """
        try:
            messagebox.showwarning(title, message, parent=parent)
        except Exception as e:
            print(f"Failed to show warning dialog: {e}")
            print(f"Original warning: {message}")
    
    def show_info_dialog(self, title: str, message: str, parent: tk.Widget = None):
        """
        Show info dialog to user
        
        Args:
            title: Dialog title
            message: Info message
            parent: Parent widget (optional)
        """
        try:
            messagebox.showinfo(title, message, parent=parent)
        except Exception as e:
            print(f"Failed to show info dialog: {e}")
            print(f"Original info: {message}")
    
    def ask_yes_no(self, title: str, message: str, parent: tk.Widget = None) -> bool:
        """
        Ask user a yes/no question
        
        Args:
            title: Dialog title
            message: Question message
            parent: Parent widget (optional)
            
        Returns:
            True if user clicked Yes, False otherwise
        """
        try:
            return messagebox.askyesno(title, message, parent=parent)
        except Exception as e:
            self.logger.error(f"Failed to show yes/no dialog: {e}")
            return False
    
    def get_log_contents(self, lines: int = 100) -> str:
        """
        Get recent log contents
        
        Args:
            lines: Number of recent lines to return
            
        Returns:
            Log contents as string
        """
        try:
            if not self.log_file.exists():
                return "Log file not found"
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                return ''.join(recent_lines)
        
        except Exception as e:
            return f"Failed to read log file: {e}"
    
    def clear_log(self):
        """Clear the log file"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(f"Log cleared at {datetime.now()}\n")
            self.logger.info("Log file cleared")
        except Exception as e:
            self.logger.error(f"Failed to clear log file: {e}")

# Global error handler instance
error_handler = ErrorHandler()

# Convenience functions
def handle_exception(exception: Exception, context: str = "Application", show_dialog: bool = True):
    """Handle an exception using the global error handler"""
    return error_handler.handle_exception(exception, context, show_dialog)

def log_error(message: str, exception: Exception = None, show_dialog: bool = False):
    """Log an error using the global error handler"""
    error_handler.log_error(message, exception, show_dialog)

def log_warning(message: str, show_dialog: bool = False):
    """Log a warning using the global error handler"""
    error_handler.log_warning(message, show_dialog)

def log_info(message: str, show_dialog: bool = False):
    """Log info using the global error handler"""
    error_handler.log_info(message, show_dialog)

def show_error(title: str, message: str, parent: tk.Widget = None):
    """Show error dialog using the global error handler"""
    error_handler.show_error_dialog(title, message, parent)

def show_warning(title: str, message: str, parent: tk.Widget = None):
    """Show warning dialog using the global error handler"""
    error_handler.show_warning_dialog(title, message, parent)

def show_info(title: str, message: str, parent: tk.Widget = None):
    """Show info dialog using the global error handler"""
    error_handler.show_info_dialog(title, message, parent)

def ask_yes_no(title: str, message: str, parent: tk.Widget = None) -> bool:
    """Ask yes/no question using the global error handler"""
    return error_handler.ask_yes_no(title, message, parent)

# Exception handler decorator
def handle_exceptions(context: str = "Function", show_dialog: bool = True, return_value: Any = None):
    """
    Decorator to handle exceptions in functions
    
    Args:
        context: Context description for error logging
        show_dialog: Whether to show error dialog
        return_value: Value to return if exception occurs
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handle_exception(e, f"{context} ({func.__name__})", show_dialog)
                return return_value
        return wrapper
    return decorator
